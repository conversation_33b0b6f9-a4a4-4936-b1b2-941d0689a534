#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script for the new evaluation metrics functionality
Tests the two new metrics:
1. "Number of Additional Issues Identified" (追加指摘された回数)
2. "Issue Classification/Category" (指摘区分)
"""

import json
import os
import tempfile
import unittest
from datetime import datetime
from quality import QualityApp
import tkinter as tk

class TestNewMetrics(unittest.TestCase):
    def setUp(self):
        """Set up test environment"""
        self.test_data_file = tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False)
        self.test_data_file.close()
        
        # Create test data with new metrics
        self.test_data = [
            {
                "id": "test001",
                "date": "2025-01-01",
                "project": "Test Project 1",
                "category": "開発",
                "rating": 4,
                "additional_issues_count": 2,
                "issue_classification": "中",
                "notes": "Test notes 1"
            },
            {
                "id": "test002", 
                "date": "2025-01-02",
                "project": "Test Project 2",
                "category": "テスト",
                "rating": 3,
                "additional_issues_count": 0,
                "issue_classification": "なし",
                "notes": "Test notes 2"
            },
            {
                "id": "test003",
                "date": "2025-01-03", 
                "project": "Test Project 3",
                "category": "レビュー",
                "rating": 2,
                "additional_issues_count": 5,
                "issue_classification": "重要",
                "notes": "Test notes 3"
            }
        ]
        
        # Write test data to file
        with open(self.test_data_file.name, 'w', encoding='utf-8') as f:
            json.dump(self.test_data, f, indent=4, ensure_ascii=False)
    
    def tearDown(self):
        """Clean up test environment"""
        if os.path.exists(self.test_data_file.name):
            os.unlink(self.test_data_file.name)
    
    def test_data_loading_with_new_metrics(self):
        """Test that data with new metrics loads correctly"""
        # Temporarily replace the data file
        original_data_file = 'quality_data.json'
        backup_exists = os.path.exists(original_data_file)
        if backup_exists:
            os.rename(original_data_file, original_data_file + '.test_backup')
        
        try:
            # Copy test data to main data file
            with open(self.test_data_file.name, 'r', encoding='utf-8') as src:
                with open(original_data_file, 'w', encoding='utf-8') as dst:
                    dst.write(src.read())
            
            # Create app instance and test data loading
            root = tk.Tk()
            root.withdraw()  # Hide the window for testing
            app = QualityApp(root)
            
            # Verify data loaded correctly
            self.assertEqual(len(app.data), 3)
            
            # Check specific entry has new metrics (find by ID since data might be sorted)
            entry1 = next((item for item in app.data if item.get("id") == "test001"), None)
            self.assertIsNotNone(entry1)
            self.assertIn('additional_issues_count', entry1)
            self.assertIn('issue_classification', entry1)
            self.assertEqual(entry1['additional_issues_count'], 2)
            self.assertEqual(entry1['issue_classification'], '中')
            
            root.destroy()
            
        finally:
            # Restore original data file
            if os.path.exists(original_data_file):
                os.unlink(original_data_file)
            if backup_exists:
                os.rename(original_data_file + '.test_backup', original_data_file)
    
    def test_backward_compatibility(self):
        """Test that old data without new metrics still works"""
        # Create old format data
        old_data = [
            {
                "id": "old001",
                "date": "2025-01-01",
                "project": "Old Project",
                "category": "開発",
                "rating": 4,
                "notes": "Old format data"
            }
        ]
        
        with open(self.test_data_file.name, 'w', encoding='utf-8') as f:
            json.dump(old_data, f, indent=4, ensure_ascii=False)
        
        # Test loading
        original_data_file = 'quality_data.json'
        backup_exists = os.path.exists(original_data_file)
        if backup_exists:
            os.rename(original_data_file, original_data_file + '.test_backup')
        
        try:
            with open(self.test_data_file.name, 'r', encoding='utf-8') as src:
                with open(original_data_file, 'w', encoding='utf-8') as dst:
                    dst.write(src.read())
            
            root = tk.Tk()
            root.withdraw()
            app = QualityApp(root)
            
            # Verify old data loaded with default values for new metrics
            self.assertEqual(len(app.data), 1)
            entry = app.data[0]
            self.assertEqual(entry['additional_issues_count'], 0)  # Default value
            self.assertEqual(entry['issue_classification'], 'なし')  # Default value
            
            root.destroy()
            
        finally:
            if os.path.exists(original_data_file):
                os.unlink(original_data_file)
            if backup_exists:
                os.rename(original_data_file + '.test_backup', original_data_file)
    
    def test_csv_export_includes_new_metrics(self):
        """Test that CSV export includes the new metric fields"""
        import csv
        import tempfile
        
        # Create a temporary CSV file
        csv_file = tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False)
        csv_file.close()
        
        try:
            # Write test data using CSV writer with new fieldnames
            fieldnames = ["id", "date", "project", "category", "rating", "additional_issues_count", "issue_classification", "notes"]
            with open(csv_file.name, "w", newline="", encoding="utf-8-sig") as csvfile:
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames, extrasaction='ignore')
                writer.writeheader()
                writer.writerows(self.test_data)
            
            # Read back and verify
            with open(csv_file.name, "r", encoding="utf-8-sig") as csvfile:
                reader = csv.DictReader(csvfile)
                rows = list(reader)
                
                self.assertEqual(len(rows), 3)
                self.assertIn('additional_issues_count', rows[0])
                self.assertIn('issue_classification', rows[0])
                self.assertEqual(rows[0]['additional_issues_count'], '2')
                self.assertEqual(rows[0]['issue_classification'], '中')
        
        finally:
            if os.path.exists(csv_file.name):
                os.unlink(csv_file.name)

def run_tests():
    """Run all tests"""
    print("Running tests for new evaluation metrics...")
    unittest.main(verbosity=2)

if __name__ == "__main__":
    run_tests()
