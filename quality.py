import tkinter as tk
from tkinter import ttk, messagebox, simpledialog, filedialog
import json
from datetime import datetime
import os
import csv # CSVエクスポート用
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import pandas as pd
import seaborn as sns
from collections import Counter
import numpy as np

DATA_FILE = "quality_data.json"

class QualityApp:
    def __init__(self, root):
        self.root = root
        self.root.title("品質管理アプリ (IT業務)")
        # self.root.geometry("850x650") # 必要に応じてウィンドウサイズ調整

        self.data = self.load_data()

        # --- UI要素の作成 ---
        # メインフレームを左右に分割
        main_frame = ttk.Frame(root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        input_frame_container = ttk.Frame(main_frame, width=350) # 左側の入力フレームの幅を固定気味に
        input_frame_container.pack(side=tk.LEFT, fill=tk.Y, padx=(0,10))
        input_frame_container.pack_propagate(False) # 幅が内容に引きずられないように

        view_frame_container = ttk.Frame(main_frame) # 右側の表示フレーム
        view_frame_container.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)


        self.create_input_frame(input_frame_container)
        self.create_view_frame(view_frame_container)
        self.create_menu()

        self.populate_view() # 初期データ表示
        self.update_category_combobox() # 初期カテゴリ候補のロード

    def load_data(self):
        if not os.path.exists(DATA_FILE):
            return []
        try:
            with open(DATA_FILE, "r", encoding="utf-8") as f:
                loaded_data = json.load(f)
                # IDがない古いデータ形式への対応 (オプション)
                for i, item in enumerate(loaded_data):
                    if "id" not in item:
                        item["id"] = datetime.now().strftime("%Y%m%d%H%M%S%f") + str(i)
                return loaded_data
        except json.JSONDecodeError:
            if os.path.getsize(DATA_FILE) > 0: # 空ファイルでなければエラー表示
                messagebox.showerror("エラー", f"{DATA_FILE}が不正な形式です。バックアップを作成し、空のデータで開始します。")
                try:
                    os.rename(DATA_FILE, DATA_FILE + ".corrupted." + datetime.now().strftime("%Y%m%d%H%M%S"))
                except OSError:
                    pass # リネーム失敗は無視
            return []
        except Exception as e:
            messagebox.showerror("エラー", f"データの読み込みに失敗しました: {e}")
            return []

    def save_data(self):
        try:
            # 念のためバックアップ
            if os.path.exists(DATA_FILE):
                backup_file = DATA_FILE + ".bak"
                with open(DATA_FILE, "r", encoding="utf-8") as f_old, open(backup_file, "w", encoding="utf-8") as f_bak:
                    f_bak.write(f_old.read())

            with open(DATA_FILE, "w", encoding="utf-8") as f:
                json.dump(self.data, f, indent=4, ensure_ascii=False)
        except Exception as e:
            messagebox.showerror("エラー", f"データの保存に失敗しました: {e}")

    def create_input_frame(self, parent_frame):
        input_frame = ttk.LabelFrame(parent_frame, text="データ入力")
        input_frame.pack(padx=5, pady=5, fill="x")

        # 日付
        ttk.Label(input_frame, text="日付:").grid(row=0, column=0, padx=5, pady=3, sticky="w")
        self.date_entry_var = tk.StringVar(value=datetime.now().strftime("%Y-%m-%d"))
        self.date_entry = ttk.Entry(input_frame, textvariable=self.date_entry_var, width=12)
        self.date_entry.grid(row=0, column=1, padx=5, pady=3, sticky="ew")
        ttk.Button(input_frame, text="今日", width=5, command=lambda: self.date_entry_var.set(datetime.now().strftime("%Y-%m-%d"))).grid(row=0, column=2, padx=5, pady=3)

        # プロジェクト/タスク名
        ttk.Label(input_frame, text="プロジェクト/タスク:").grid(row=1, column=0, padx=5, pady=3, sticky="w")
        self.project_entry = ttk.Entry(input_frame, width=30)
        self.project_entry.grid(row=1, column=1, columnspan=2, padx=5, pady=3, sticky="ew")

        # カテゴリ
        ttk.Label(input_frame, text="カテゴリ:").grid(row=2, column=0, padx=5, pady=3, sticky="w")
        self.category_var = tk.StringVar()
        self.category_combo = ttk.Combobox(input_frame, textvariable=self.category_var, width=28)
        self.category_combo.grid(row=2, column=1, columnspan=2, padx=5, pady=3, sticky="ew")
        self.category_combo.bind('<FocusIn>', lambda e: self.update_category_combobox())


        # 品質指標 (例: 自己評価 1-5)
        ttk.Label(input_frame, text="自己評価 (1-5):").grid(row=3, column=0, padx=5, pady=3, sticky="w")
        self.rating_var = tk.IntVar(value=3) # デフォルト値
        rating_frame = ttk.Frame(input_frame)
        rating_frame.grid(row=3, column=1, columnspan=2, padx=5, pady=3, sticky="w")
        for i in range(1, 6):
            ttk.Radiobutton(rating_frame, text=str(i), variable=self.rating_var, value=i).pack(side="left", padx=2)

        # 詳細/メモ
        ttk.Label(input_frame, text="詳細/メモ:").grid(row=4, column=0, padx=5, pady=3, sticky="nw")
        self.notes_text = tk.Text(input_frame, height=8, width=30) # 高さを調整
        self.notes_text.grid(row=4, column=1, columnspan=2, padx=5, pady=3, sticky="ew")
        notes_scrollbar = ttk.Scrollbar(input_frame, orient="vertical", command=self.notes_text.yview)
        notes_scrollbar.grid(row=4, column=3, sticky="ns")
        self.notes_text.config(yscrollcommand=notes_scrollbar.set)

        # 保存ボタン
        save_button = ttk.Button(input_frame, text="保存", command=self.add_entry)
        save_button.grid(row=5, column=1, columnspan=2, pady=10)

        input_frame.columnconfigure(1, weight=1)

    def add_entry(self):
        date_str = self.date_entry_var.get()
        project = self.project_entry.get().strip()
        category = self.category_var.get().strip()
        rating = self.rating_var.get()
        notes = self.notes_text.get("1.0", tk.END).strip()

        if not date_str or not project:
            messagebox.showwarning("入力エラー", "日付とプロジェクト/タスクは必須です。")
            return

        try:
            datetime.strptime(date_str, "%Y-%m-%d")
        except ValueError:
            messagebox.showwarning("入力エラー", "日付はYYYY-MM-DD形式で入力してください。")
            return

        entry = {
            "id": datetime.now().strftime("%Y%m%d%H%M%S%f"), # ユニークID
            "date": date_str,
            "project": project,
            "category": category,
            "rating": rating,
            "notes": notes
        }
        self.data.append(entry)
        self.data.sort(key=lambda x: x["date"], reverse=True)
        self.save_data()
        self.populate_view()
        self.clear_input_fields()
        self.update_category_combobox()
        messagebox.showinfo("成功", "データを保存しました。")

    def clear_input_fields(self):
        self.date_entry_var.set(datetime.now().strftime("%Y-%m-%d"))
        self.project_entry.delete(0, tk.END)
        self.category_var.set("") # Comboboxクリア
        self.rating_var.set(3)
        self.notes_text.delete("1.0", tk.END)
        self.project_entry.focus() # 次の入力のためにフォーカス

    def update_category_combobox(self):
        current_value = self.category_var.get()
        categories = sorted(list(set(item.get("category", "").strip() for item in self.data if item.get("category", "").strip())))
        if not categories: # データがない場合やカテゴリが空の場合のデフォルト
            categories = ["開発", "設計", "テスト", "レビュー", "障害対応", "会議", "資料作成", "自己学習", "その他"]
        self.category_combo['values'] = categories
        if current_value in categories:
             self.category_var.set(current_value)
        elif categories and not current_value: # 空で候補があれば最初のものを選択
             pass # 空のままにするか、何か選ぶかはお好みで

    def create_view_frame(self, parent_frame):
        view_frame = ttk.LabelFrame(parent_frame, text="記録一覧 (最新順)")
        view_frame.pack(padx=5, pady=5, fill="both", expand=True)

        filter_frame = ttk.Frame(view_frame)
        filter_frame.pack(fill="x", pady=5, padx=5)
        ttk.Label(filter_frame, text="検索:").pack(side="left", padx=(0,5))
        self.search_entry = ttk.Entry(filter_frame, width=30)
        self.search_entry.pack(side="left", padx=5, fill="x", expand=True)
        self.search_entry.bind("<Return>", lambda e: self.filter_view()) # Enterでも検索
        search_button = ttk.Button(filter_frame, text="実行", command=self.filter_view)
        search_button.pack(side="left", padx=5)
        clear_button = ttk.Button(filter_frame, text="クリア", command=self.clear_filter)
        clear_button.pack(side="left", padx=5)

        columns = ("date", "project", "category", "rating", "notes_preview")
        self.tree = ttk.Treeview(view_frame, columns=columns, show="headings")
        self.tree.heading("date", text="日付")
        self.tree.heading("project", text="プロジェクト/タスク")
        self.tree.heading("category", text="カテゴリ")
        self.tree.heading("rating", text="評価")
        self.tree.heading("notes_preview", text="メモ抜粋")

        self.tree.column("date", width=80, anchor="w", stretch=False)
        self.tree.column("project", width=180, anchor="w")
        self.tree.column("category", width=100, anchor="w")
        self.tree.column("rating", width=40, anchor="center", stretch=False)
        self.tree.column("notes_preview", width=200, anchor="w")

        vsb = ttk.Scrollbar(view_frame, orient="vertical", command=self.tree.yview)
        hsb = ttk.Scrollbar(view_frame, orient="horizontal", command=self.tree.xview)
        self.tree.configure(yscrollcommand=vsb.set, xscrollcommand=hsb.set)

        vsb.pack(side="right", fill="y")
        hsb.pack(side="bottom", fill="x")
        self.tree.pack(fill="both", expand=True, pady=(0,5), padx=5)

        self.tree.bind("<Double-1>", self.show_entry_details)
        self.tree_context_menu = tk.Menu(self.root, tearoff=0)
        self.tree_context_menu.add_command(label="選択した項目を削除", command=self.delete_selected_entry)
        self.tree_context_menu.add_command(label="選択した項目を編集", command=self.edit_selected_entry)
        self.tree.bind("<Button-3>", self.show_context_menu) # Windows/Linux
        self.tree.bind("<Button-2>", self.show_context_menu) # macOS (optional)


    def populate_view(self, filtered_data=None):
        for i in self.tree.get_children():
            self.tree.delete(i)

        data_to_show = filtered_data if filtered_data is not None else self.data
        # 念のため、表示前にソート
        data_to_show.sort(key=lambda x: (x.get("date", "0000-00-00"), x.get("id", "")), reverse=True)


        for entry in data_to_show:
            notes_preview = entry.get("notes", "")
            if len(notes_preview) > 40:
                notes_preview = notes_preview[:37] + "..."
            self.tree.insert("", "end", values=(
                entry.get("date", ""),
                entry.get("project", ""),
                entry.get("category", ""),
                entry.get("rating", ""),
                notes_preview
            ), iid=entry.get("id", ""))

    def filter_view(self):
        search_term = self.search_entry.get().lower().strip()
        if not search_term:
            self.populate_view()
            return

        filtered_data = [
            entry for entry in self.data
            if search_term in entry.get("project", "").lower() or \
               search_term in entry.get("category", "").lower() or \
               search_term in entry.get("notes", "").lower() or \
               search_term in entry.get("date", "").lower() # 日付でも検索
        ]
        self.populate_view(filtered_data)

    def clear_filter(self):
        self.search_entry.delete(0, tk.END)
        self.populate_view()

    def show_entry_details(self, event):
        item_id = self.tree.focus()
        if not item_id:
            return
        selected_item_data = next((item for item in self.data if item.get("id") == item_id), None)
        if selected_item_data:
            self.display_detail_window(selected_item_data)

    def display_detail_window(self, item_data, is_edit_mode=False):
        detail_window = tk.Toplevel(self.root)
        mode_text = "編集" if is_edit_mode else "詳細"
        detail_window.title(f"記録{mode_text}: {item_data.get('project', '')[:20]}")
        detail_window.geometry("600x450") # 少し大きめに
        detail_window.transient(self.root) # 親ウィンドウの上に表示
        detail_window.grab_set() # モーダルっぽくする

        form_frame = ttk.Frame(detail_window)
        form_frame.pack(padx=10, pady=10, fill="both", expand=True)

        fields = {
            "date_var": tk.StringVar(value=item_data.get("date")),
            "project_var": tk.StringVar(value=item_data.get("project")),
            "category_var": tk.StringVar(value=item_data.get("category")),
            "rating_var": tk.IntVar(value=item_data.get("rating", 3)),
        }

        ttk.Label(form_frame, text="日付:").grid(row=0, column=0, sticky="w", pady=2)
        date_e = ttk.Entry(form_frame, textvariable=fields["date_var"], state="readonly" if not is_edit_mode else "normal")
        date_e.grid(row=0, column=1, sticky="ew", pady=2)

        ttk.Label(form_frame, text="プロジェクト/タスク:").grid(row=1, column=0, sticky="w", pady=2)
        project_e = ttk.Entry(form_frame, textvariable=fields["project_var"], state="readonly" if not is_edit_mode else "normal")
        project_e.grid(row=1, column=1, sticky="ew", pady=2)

        ttk.Label(form_frame, text="カテゴリ:").grid(row=2, column=0, sticky="w", pady=2)
        # 既存のカテゴリリストを取得
        categories = sorted(list(set(item.get("category", "").strip() for item in self.data if item.get("category", "").strip())))
        # カテゴリがない場合はデフォルト値を使用
        if not categories:
            categories = ["開発", "設計", "テスト", "レビュー", "障害対応", "会議", "資料作成", "自己学習", "その他"]
        
        # 重要: 現在のカテゴリがリストにない場合は追加
        current_category = item_data.get("category", "")
        if current_category and current_category not in categories:
            categories.append(current_category)
            categories.sort()
        
        # Comboboxの状態設定
        state = "readonly" if not is_edit_mode else "normal"
        
        # 先にStringVarに値をセット
        fields["category_var"].set(current_category)
          # そのあとでComboboxを作成
        category_c = ttk.Combobox(form_frame, textvariable=fields["category_var"], values=categories, state=state)
        category_c.grid(row=2, column=1, sticky="ew", pady=2)
        
        # Comboboxがreadonly状態でも値を選択できるようにする
        if not is_edit_mode:
            category_c.bind("<<ComboboxSelected>>", lambda e: fields["category_var"].set(category_c.get()))

        ttk.Label(form_frame, text="自己評価 (1-5):").grid(row=3, column=0, sticky="w", pady=2)
        rating_f = ttk.Frame(form_frame)
        rating_f.grid(row=3, column=1, sticky="ew", pady=2)
        for i in range(1, 6):
            rb = ttk.Radiobutton(rating_f, text=str(i), variable=fields["rating_var"], value=i, state="disabled" if not is_edit_mode else "normal")
            rb.pack(side="left", padx=2)

        ttk.Label(form_frame, text="詳細/メモ:").grid(row=4, column=0, sticky="nw", pady=2)
        notes_t = tk.Text(form_frame, height=10, wrap="word")
        notes_t.grid(row=4, column=1, sticky="nsew", pady=2)
        notes_t.insert("1.0", item_data.get("notes", ""))
        notes_t.config(state="disabled" if not is_edit_mode else "normal")

        form_frame.columnconfigure(1, weight=1)
        form_frame.rowconfigure(4, weight=1)

        button_frame = ttk.Frame(detail_window)
        button_frame.pack(pady=10)

        if is_edit_mode:
            def save_changes():
                # バリデーション
                new_date = fields["date_var"].get()
                new_project = fields["project_var"].get().strip()
                if not new_date or not new_project:
                    messagebox.showerror("エラー", "日付とプロジェクト名は必須です。", parent=detail_window)
                    return
                try:
                    datetime.strptime(new_date, "%Y-%m-%d")
                except ValueError:
                    messagebox.showerror("エラー", "日付はYYYY-MM-DD形式で入力してください。", parent=detail_window)
                    return

                # データ更新
                for i, d in enumerate(self.data):
                    if d["id"] == item_data["id"]:
                        self.data[i]["date"] = new_date
                        self.data[i]["project"] = new_project
                        self.data[i]["category"] = fields["category_var"].get().strip()
                        self.data[i]["rating"] = fields["rating_var"].get()
                        self.data[i]["notes"] = notes_t.get("1.0", tk.END).strip()
                        break
                self.save_data()
                self.populate_view()
                self.update_category_combobox()
                messagebox.showinfo("成功", "変更を保存しました。", parent=detail_window)
                detail_window.destroy()

            ttk.Button(button_frame, text="保存", command=save_changes).pack(side="left", padx=5)
        ttk.Button(button_frame, text="閉じる", command=detail_window.destroy).pack(side="left", padx=5)


    def show_context_menu(self, event):
        iid = self.tree.identify_row(event.y)
        if iid:
            self.tree.selection_set(iid) # 右クリックされた行を選択状態にする
            self.tree.focus(iid) # フォーカスも当てる
            self.tree_context_menu.post(event.x_root, event.y_root)

    def delete_selected_entry(self):
        selected_item_id = self.tree.focus()
        if not selected_item_id:
            messagebox.showwarning("注意", "削除する項目を選択してください。")
            return

        if messagebox.askyesno("確認", "選択した項目を本当に削除しますか？\nこの操作は元に戻せません。"):
            self.data = [item for item in self.data if item.get("id") != selected_item_id]
            self.save_data()
            self.populate_view()
            self.update_category_combobox()
            messagebox.showinfo("成功", "項目を削除しました。")

    def edit_selected_entry(self):
        item_id = self.tree.focus()
        if not item_id:
            messagebox.showwarning("注意", "編集する項目を選択してください。")
            return
        selected_item_data = next((item for item in self.data if item.get("id") == item_id), None)
        if selected_item_data:
            self.display_detail_window(selected_item_data, is_edit_mode=True)

    def create_menu(self):
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)

        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="ファイル", menu=file_menu)
        file_menu.add_command(label="データのエクスポート (CSV)", command=self.export_to_csv)
        # file_menu.add_command(label="データのインポート (CSV, 未実装)", command=self.import_from_csv, state="disabled")
        file_menu.add_separator()
        file_menu.add_command(label="終了", command=self.root.quit)

        # 新增分析菜单
        analysis_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="分析", menu=analysis_menu)
        analysis_menu.add_command(label="品質チャート分析", command=self.show_chart_analysis)

        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="ヘルプ", menu=help_menu)
        help_menu.add_command(label="このアプリについて", command=self.show_about)

    def export_to_csv(self):
        if not self.data:
            messagebox.showinfo("情報", "エクスポートするデータがありません。")
            return

        filepath = filedialog.asksaveasfilename(
            defaultextension=".csv",
            filetypes=[("CSVファイル", "*.csv"), ("すべてのファイル", "*.*")],
            title="CSVファイルとしてエクスポート",
            initialfile=f"quality_export_{datetime.now().strftime('%Y%m%d')}.csv"
        )
        if not filepath:
            return

        try:
            fieldnames = ["id", "date", "project", "category", "rating", "notes"]
            with open(filepath, "w", newline="", encoding="utf-8-sig") as csvfile: # utf-8-sig for Excel
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames, extrasaction='ignore')
                writer.writeheader()
                # ソートしてエクスポート (日付昇順が良いかもしれない)
                sorted_data = sorted(self.data, key=lambda x: (x.get("date", ""), x.get("id", "")))
                writer.writerows(sorted_data)
            messagebox.showinfo("成功", f"データを {os.path.basename(filepath)} にエクスポートしました。")
        except Exception as e:
            messagebox.showerror("エクスポートエラー", f"CSVファイルのエクスポートに失敗しました: {e}")

    # def import_from_csv(self):
    #     messagebox.showinfo("未実装", "CSVインポート機能はまだ実装されていません。")

    def show_about(self):
        messagebox.showinfo(
            "このアプリについて",
            "品質管理アプリ (IT業務向け)\n\n"
            "バージョン: 0.3\n"
            "作成協力: AI (Claude)\n\n"
            "日々の業務結果を記録し、振り返りに役立てるためのシンプルなデスクトップアプリケーションです。\n"
            f"データは {os.path.abspath(DATA_FILE)} に保存されています。"
        )

    def show_chart_analysis(self):
        """显示品质图表分析窗口"""
        if not self.data:
            messagebox.showinfo("情報", "分析用のデータがありません。")
            return

        # 创建分析窗口
        analysis_window = tk.Toplevel(self.root)
        analysis_window.title("品質チャート分析")
        analysis_window.geometry("1200x800")
        analysis_window.transient(self.root)

        # 创建主框架
        main_frame = ttk.Frame(analysis_window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 创建选项卡
        notebook = ttk.Notebook(main_frame)
        notebook.pack(fill=tk.BOTH, expand=True)

        # 时间趋势分析标签页
        trend_frame = ttk.Frame(notebook)
        notebook.add(trend_frame, text="時間別品質推移")
        self.create_trend_chart(trend_frame)

        # 类别分析标签页
        category_frame = ttk.Frame(notebook)
        notebook.add(category_frame, text="カテゴリ別分析")
        self.create_category_chart(category_frame)

        # 评分分布标签页
        rating_frame = ttk.Frame(notebook)
        notebook.add(rating_frame, text="評価分布")
        self.create_rating_chart(rating_frame)

        # 月度总结标签页
        monthly_frame = ttk.Frame(notebook)
        notebook.add(monthly_frame, text="月次サマリー")
        self.create_monthly_summary(monthly_frame)

        # 关闭按钮
        close_frame = ttk.Frame(analysis_window)
        close_frame.pack(pady=5)
        ttk.Button(close_frame, text="閉じる", command=analysis_window.destroy).pack()

    def create_trend_chart(self, parent):
        """创建时间趋势图表"""
        # 设置日文字体
        self.setup_japanese_font()
        plt.rcParams['axes.unicode_minus'] = False

        # 准备数据
        df = pd.DataFrame(self.data)
        df['date'] = pd.to_datetime(df['date'])
        df = df.sort_values('date')

        # 创建图形
        fig, axes = plt.subplots(2, 1, figsize=(12, 8))

        # 每日评分趋势
        daily_avg = df.groupby('date')['rating'].mean()
        axes[0].plot(daily_avg.index, daily_avg.values, marker='o', linewidth=2, markersize=6)
        axes[0].set_title('日別品質評価推移', fontsize=14, fontweight='bold')
        axes[0].set_ylabel('平均評価', fontsize=12)
        axes[0].grid(True, alpha=0.3)
        axes[0].set_ylim(1, 5)

        # 添加趋势线
        if len(daily_avg) > 1:
            z = np.polyfit(range(len(daily_avg)), daily_avg.values, 1)
            p = np.poly1d(z)
            axes[0].plot(daily_avg.index, p(range(len(daily_avg))), 
                        "r--", alpha=0.8, linewidth=2, label=f'トレンド線 (傾き: {z[0]:.3f})')
            axes[0].legend()

        # 工作项目数量趋势
        daily_count = df.groupby('date').size()
        axes[1].bar(daily_count.index, daily_count.values, alpha=0.7, color='lightblue')
        axes[1].set_title('日別作業項目数', fontsize=14, fontweight='bold')
        axes[1].set_ylabel('項目数', fontsize=12)
        axes[1].set_xlabel('日付', fontsize=12)
        axes[1].grid(True, alpha=0.3)

        plt.tight_layout()

        # 将图表嵌入到 tkinter 窓口
        canvas = FigureCanvasTkAgg(fig, parent)
        canvas.draw()
        canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

    def create_category_chart(self, parent):
        """创建类别分析图表"""
        self.setup_japanese_font()
        plt.rcParams['axes.unicode_minus'] = False

        df = pd.DataFrame(self.data)
        
        # 创建图形
        fig, axes = plt.subplots(2, 2, figsize=(12, 10))
        
        # 类别分布饼图
        category_counts = df['category'].value_counts()
        axes[0, 0].pie(category_counts.values, labels=category_counts.index, autopct='%1.1f%%', startangle=90)
        axes[0, 0].set_title('カテゴリ別分布', fontsize=12, fontweight='bold')

        # 类别平均评分
        category_ratings = df.groupby('category')['rating'].mean().sort_values(ascending=True)
        bars = axes[0, 1].barh(range(len(category_ratings)), category_ratings.values)
        axes[0, 1].set_yticks(range(len(category_ratings)))
        axes[0, 1].set_yticklabels(category_ratings.index)
        axes[0, 1].set_title('カテゴリ別平均評価', fontsize=12, fontweight='bold')
        axes[0, 1].set_xlabel('平均評価')
        axes[0, 1].grid(True, alpha=0.3)
        
        # 为每个条形添加数值标签
        for i, bar in enumerate(bars):
            width = bar.get_width()
            axes[0, 1].text(width + 0.05, bar.get_y() + bar.get_height()/2, 
                           f'{width:.2f}', ha='left', va='center')

        # 类别项目数量
        axes[1, 0].bar(category_counts.index, category_counts.values, color='lightgreen')
        axes[1, 0].set_title('カテゴリ別項目数', fontsize=12, fontweight='bold')
        axes[1, 0].set_ylabel('項目数')
        axes[1, 0].tick_params(axis='x', rotation=45)
        axes[1, 0].grid(True, alpha=0.3)

        # 类别评分箱线图
        categories = df['category'].unique()
        rating_data = [df[df['category'] == cat]['rating'].values for cat in categories]
        axes[1, 1].boxplot(rating_data, labels=categories)
        axes[1, 1].set_title('カテゴリ別評価分布', fontsize=12, fontweight='bold')
        axes[1, 1].set_ylabel('評価')
        axes[1, 1].tick_params(axis='x', rotation=45)
        axes[1, 1].grid(True, alpha=0.3)

        plt.tight_layout()

        canvas = FigureCanvasTkAgg(fig, parent)
        canvas.draw()
        canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

    def create_rating_chart(self, parent):
        """创建评分分布图表"""
        self.setup_japanese_font()
        plt.rcParams['axes.unicode_minus'] = False

        df = pd.DataFrame(self.data)
        
        fig, axes = plt.subplots(2, 2, figsize=(12, 10))

        # 评分分布直方图
        axes[0, 0].hist(df['rating'], bins=5, range=(0.5, 5.5), alpha=0.7, color='skyblue', edgecolor='black')
        axes[0, 0].set_title('評価分布', fontsize=12, fontweight='bold')
        axes[0, 0].set_xlabel('評価')
        axes[0, 0].set_ylabel('頻度')
        axes[0, 0].set_xticks(range(1, 6))
        axes[0, 0].grid(True, alpha=0.3)

        # 评分比例饼图
        rating_counts = df['rating'].value_counts().sort_index()
        axes[0, 1].pie(rating_counts.values, labels=[f'評価{i}' for i in rating_counts.index], 
                      autopct='%1.1f%%', startangle=90)
        axes[0, 1].set_title('評価比率', fontsize=12, fontweight='bold')

        # 时间序列评分分布
        df['date'] = pd.to_datetime(df['date'])
        df_sorted = df.sort_values('date')
        
        # 创建移动平均线
        window_size = min(7, len(df_sorted))  # 7日移动平均或数据点数
        if len(df_sorted) >= window_size:
            df_sorted['rolling_avg'] = df_sorted['rating'].rolling(window=window_size, center=True).mean()
            axes[1, 0].plot(df_sorted['date'], df_sorted['rolling_avg'], 
                           label=f'{window_size}日移動平均', linewidth=2, color='red')
        
        axes[1, 0].scatter(df_sorted['date'], df_sorted['rating'], alpha=0.6, s=50)
        axes[1, 0].set_title('時系列評価推移', fontsize=12, fontweight='bold')
        axes[1, 0].set_ylabel('評価')
        axes[1, 0].set_xlabel('日付')
        axes[1, 0].grid(True, alpha=0.3)
        axes[1, 0].legend()

        # 评分统计信息
        stats_text = f"""統計情報:
平均評価: {df['rating'].mean():.2f}
中央値: {df['rating'].median():.2f}
標準偏差: {df['rating'].std():.2f}
最高評価: {df['rating'].max()}
最低評価: {df['rating'].min()}
データ数: {len(df)}"""
        
        axes[1, 1].text(0.1, 0.5, stats_text, transform=axes[1, 1].transAxes, 
                       fontsize=12, verticalalignment='center',
                       bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgray", alpha=0.5))
        axes[1, 1].set_xlim(0, 1)
        axes[1, 1].set_ylim(0, 1)
        axes[1, 1].axis('off')

        plt.tight_layout()

        canvas = FigureCanvasTkAgg(fig, parent)
        canvas.draw()
        canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

    def create_monthly_summary(self, parent):
        """创建月度总结"""
        self.setup_japanese_font()
        plt.rcParams['axes.unicode_minus'] = False

        df = pd.DataFrame(self.data)
        df['date'] = pd.to_datetime(df['date'])
        df['year_month'] = df['date'].dt.to_period('M')

        fig, axes = plt.subplots(2, 2, figsize=(12, 10))

        # 月度平均评分
        monthly_avg = df.groupby('year_month')['rating'].mean()
        axes[0, 0].plot(range(len(monthly_avg)), monthly_avg.values, marker='o', linewidth=2)
        axes[0, 0].set_title('月別平均評価', fontsize=12, fontweight='bold')
        axes[0, 0].set_ylabel('平均評価')
        axes[0, 0].set_xticks(range(len(monthly_avg)))
        axes[0, 0].set_xticklabels([str(m) for m in monthly_avg.index], rotation=45)
        axes[0, 0].grid(True, alpha=0.3)

        # 月度项目数量
        monthly_count = df.groupby('year_month').size()
        axes[0, 1].bar(range(len(monthly_count)), monthly_count.values, color='lightcoral')
        axes[0, 1].set_title('月別項目数', fontsize=12, fontweight='bold')
        axes[0, 1].set_ylabel('項目数')
        axes[0, 1].set_xticks(range(len(monthly_count)))
        axes[0, 1].set_xticklabels([str(m) for m in monthly_count.index], rotation=45)
        axes[0, 1].grid(True, alpha=0.3)

        # 月度类别分布热力图
        monthly_category = df.groupby(['year_month', 'category']).size().unstack(fill_value=0)
        if not monthly_category.empty:
            im = axes[1, 0].imshow(monthly_category.T.values, cmap='YlOrRd', aspect='auto')
            axes[1, 0].set_title('月別カテゴリ分布', fontsize=12, fontweight='bold')
            axes[1, 0].set_xticks(range(len(monthly_category.index)))
            axes[1, 0].set_xticklabels([str(m) for m in monthly_category.index], rotation=45)
            axes[1, 0].set_yticks(range(len(monthly_category.columns)))
            axes[1, 0].set_yticklabels(monthly_category.columns)
            plt.colorbar(im, ax=axes[1, 0])

        # 改进趋势分析
        improvement_text = self.calculate_improvement_trend(df)
        axes[1, 1].text(0.1, 0.5, improvement_text, transform=axes[1, 1].transAxes,
                       fontsize=11, verticalalignment='center',
                       bbox=dict(boxstyle="round,pad=0.3", facecolor="lightblue", alpha=0.5))
        axes[1, 1].set_xlim(0, 1)
        axes[1, 1].set_ylim(0, 1)
        axes[1, 1].axis('off')

        plt.tight_layout()

        canvas = FigureCanvasTkAgg(fig, parent)
        canvas.draw()
        canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

    def calculate_improvement_trend(self, df):
        """计算改进趋势分析"""
        if len(df) < 2:
            return "分析用のデータが不足しています"

        # 计算最近趋势
        df_sorted = df.sort_values('date')
        recent_data = df_sorted.tail(min(10, len(df_sorted)))
        early_data = df_sorted.head(min(10, len(df_sorted)))

        recent_avg = recent_data['rating'].mean()
        early_avg = early_data['rating'].mean()
        improvement = recent_avg - early_avg

        # 最佳和最差类别
        category_avg = df.groupby('category')['rating'].mean()
        best_category = category_avg.idxmax()
        worst_category = category_avg.idxmin()

        # 最活跃的日期
        daily_count = df.groupby('date').size()
        most_active_date = daily_count.idxmax()

        trend_text = f"""改善分析:

最近の傾向:
{'改善' if improvement > 0 else '悪化' if improvement < 0 else '変化なし'} 
({improvement:+.2f}ポイント)

最高カテゴリ: {best_category} 
(平均{category_avg[best_category]:.2f})

最低カテゴリ: {worst_category} 
(平均{category_avg[worst_category]:.2f})

最も活発な日: {most_active_date}
({daily_count[most_active_date]}項目)

総データ期間: 
{df['date'].min().strftime('%Y-%m-%d')} ～ 
{df['date'].max().strftime('%Y-%m-%d')}"""

        return trend_text

    def setup_japanese_font(self):
        """设置支持日文显示的字体"""
        import matplotlib.font_manager as fm
        
        # 尝试找到支持日文的字体
        japanese_fonts = []
        for font in fm.fontManager.ttflist:
            font_name = font.name.lower()
            if any(name in font_name for name in ['meiryo', 'ms gothic', 'yu gothic', 'hiragino', 'noto']):
                japanese_fonts.append(font.name)
        
        # 设置字体优先级
        if japanese_fonts:
            plt.rcParams['font.family'] = japanese_fonts[0]
        else:
            # 如果没有找到专门的日文字体，使用通用Unicode字体
            plt.rcParams['font.family'] = ['Arial Unicode MS', 'DejaVu Sans']
        
        # 确保负号正常显示
        plt.rcParams['axes.unicode_minus'] = False

if __name__ == "__main__":
    root = tk.Tk()
    app = QualityApp(root)
    root.mainloop()